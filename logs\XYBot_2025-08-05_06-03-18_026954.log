2025-08-05 06:03:19 | SUCCESS | 读取主设置成功
2025-08-05 06:03:19 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-05 06:03:19 | INFO | 2025/08/05 06:03:19 GetRedisAddr: 127.0.0.1:6379
2025-08-05 06:03:19 | INFO | 2025/08/05 06:03:19 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-05 06:03:19 | INFO | 2025/08/05 06:03:19 Server start at :9000
2025-08-05 06:03:19 | SUCCESS | WechatAPI服务已启动
2025-08-05 06:03:20 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-05 06:03:20 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-05 06:03:20 | SUCCESS | 登录成功
2025-08-05 06:03:20 | SUCCESS | 已开启自动心跳
2025-08-05 06:03:20 | INFO | 成功加载表情映射文件，共 552 条记录
2025-08-05 06:03:20 | SUCCESS | 数据库初始化成功
2025-08-05 06:03:20 | SUCCESS | 定时任务已启动
2025-08-05 06:03:20 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-05 06:03:20 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 06:03:21 | INFO | 播客API初始化成功
2025-08-05 06:03:21 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 06:03:21 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 06:03:21 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-05 06:03:21 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-05 06:03:21 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-05 06:03:21 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-05 06:03:21 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-05 06:03:21 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-05 06:03:21 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-05 06:03:21 | INFO | [ChatSummary] 数据库初始化成功
2025-08-05 06:03:21 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-05 06:03:21 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-05 06:03:21 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-05 06:03:21 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-05 06:03:21 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-05 06:03:21 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-05 06:03:21 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-05 06:03:21 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-05 06:03:22 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-05 06:03:22 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-05 06:03:22 | DEBUG |   - 启用状态: True
2025-08-05 06:03:22 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-05 06:03:22 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-05 06:03:22 | DEBUG |   - Web ID: 7532989324985157172
2025-08-05 06:03:22 | DEBUG |   - Cookies配置: 已配置
2025-08-05 06:03:22 | DEBUG |   - 限制机制: 已禁用
2025-08-05 06:03:22 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-05 06:03:22 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-08-05 06:03:22 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-05 06:03:22 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-05 06:03:22 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-05 06:03:22 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-05 06:03:22 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-05 06:03:22 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 06:03:22 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-05 06:03:22 | INFO | [RenameReminder] 开始启用插件...
2025-08-05 06:03:22 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-05 06:03:22 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-05 06:03:22 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-05 06:03:22 | INFO | 已设置检查间隔为 3600 秒
2025-08-05 06:03:22 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-05 06:03:23 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-05 06:03:23 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-05 06:03:23 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-05 06:03:23 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-05 06:03:24 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-05 06:03:24 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 06:03:24 | INFO | [yuanbao] 插件初始化完成
2025-08-05 06:03:24 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-05 06:03:24 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-05 06:03:24 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-05 06:03:24 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-05 06:03:24 | INFO | 处理堆积消息中
2025-08-05 06:03:24 | SUCCESS | 处理堆积消息完毕
2025-08-05 06:03:24 | SUCCESS | 开始处理消息
2025-08-05 06:03:30 | DEBUG | 收到消息: {'MsgId': 5325329, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 双倍快乐'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754345019, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_a+x8faoQ|v1_4/7nqsHY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 双倍快乐', 'NewMsgId': 8962197037700396190, 'MsgSeq': 871427272}
2025-08-05 06:03:30 | INFO | 收到文本消息: 消息ID:5325329 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 双倍快乐
2025-08-05 06:03:30 | DEBUG | [DouBaoImageToImage] 收到文本消息: '找视频 双倍快乐' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 06:03:30 | DEBUG | [DouBaoImageToImage] 命令解析: ['找视频', '双倍快乐']
2025-08-05 06:03:31 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-05 06:03:31 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 双倍快乐 from wxid_ubbh6q832tcs21
2025-08-05 06:03:31 | DEBUG | [DoubaoVideoSearch] 开始处理视频搜索请求，用户: wxid_ubbh6q832tcs21
2025-08-05 06:03:31 | INFO | [DoubaoVideoSearch] 开始搜索视频，关键词: '双倍快乐'
2025-08-05 06:03:31 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 双倍快乐
2025-08-05 06:03:31 | DEBUG | [DoubaoVideoSearch] 调试信息 - 设备ID: 7532989318484657699, Web ID: 7532989324985157172
2025-08-05 06:03:31 | DEBUG | [DoubaoVideoSearch] 调试信息 - Cookies长度: 2200
2025-08-05 06:03:31 | DEBUG | [DoubaoVideoSearch] 第1次尝试，开始构造请求参数
2025-08-05 06:03:31 | DEBUG | [DoubaoVideoSearch] 生成会话ID: local_1754345011089, 消息ID: 36646b93-8946-470b-bfb1-5110fca9816d
2025-08-05 06:03:31 | DEBUG | [DoubaoVideoSearch] 构造搜索查询: 帮我找一些关于双倍快乐的抖音视频
2025-08-05 06:03:31 | DEBUG | [DoubaoVideoSearch] 请求头: {'Host': 'www.doubao.com', 'Connection': 'keep-alive', 'x-flow-trace': '04-b033c18b1fe046bf-26d19f890e2744e0-01', 'sec-ch-ua-platform': '"Android"', 'sec-ch-ua': '"Chromium";v="130", "Android WebView";v="130", "Not?A_Brand";v="99"', 'sec-ch-ua-mobile': '?1', 'Agw-Js-Conv': 'str, str', 'last-event-id': 'undefined', 'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PJD110 Build/AP3A.240617.008) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36', 'content-type': 'application/json', 'Accept': '*/*', 'Origin': 'https://www.doubao.com', 'X-Requested-With': 'mark.via', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'https://www.doubao.com/chat/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Cookie': 'i18next=zh; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MTQ5MzU5NjkzNjgwNzQxfQ==.yuE0xZkkRp6rqlY66YCqWrFlfN//9LZZZ7S0sT8jJho=; flow_user_country=CN; ttcid=9998073e514b46379ccfd15657ffa06c33; s_v_web_id=verify_mdqh7kzd_ZkQmYYgh_AW2y_4mCs_95OU_aDZp9boGLZ1u; passport_csrf_token=23cc9202dee0bd3af72b1d67305320a1; passport_csrf_token_default=23cc9202dee0bd3af72b1d67305320a1; hook_slardar_session_id=2025073105274644B91E1243B31013D640,ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910866%7Cd0f892d1e4c839a868e20cdb539884dac45992241cb359c30ef040897bbe4427; passport_mfa_token=CjGPhXlg8y7V4IKl5gnYMvo0%2BqNhIn32FEYWNz6gkjjYI4X8hnKQYV7Unk%2BU0WC%2FzeuwGkoKPAAAAAAAAAAAAABPS%2FOfnAf%2F4h5UzB7KpogdYZuez0H1rnw99uy9NGTKi1MfFnCWCMxSVLE%2B%2FXAZYG41LRChlfgNGPax0WwgAiIBAyxRQuI%3D; d_ticket=3ba103731f1417bb3d92f65489c2cf384b9ef; odin_tt=ceee7eb54d94684dae2543622104f0c4c54b16bf7bf53e2998c4381989c54b7a65d33009560a1c9211cc7ee74550542d19171565610c7c33ca884b16e4c1d1b0; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=1e0467e7ce1de60752facaf3e7239ed9%2C; passport_auth_status_ss=1e0467e7ce1de60752facaf3e7239ed9%2C; sid_guard=53c1e5576dbeb67c1f781749fa771e22%7C1753910897%7C5184000%7CSun%2C+28-Sep-2025+21%3A28%3A17+GMT; uid_tt=b615a4154b708bbf57f415bfbf358f8e; uid_tt_ss=b615a4154b708bbf57f415bfbf358f8e; sid_tt=53c1e5576dbeb67c1f781749fa771e22; sessionid=53c1e5576dbeb67c1f781749fa771e22; sessionid_ss=53c1e5576dbeb67c1f781749fa771e22; session_tlb_tag=sttt%7C6%7CU8HlV22-tnwfeBdJ-nceIv_________SzM3yFwLkszo23AKHohjjcpT0MVXz8bPDuNRWJVotL34%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; ssid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; flow_ssr_sidebar_expand=1; ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910898%7C7dcd3259bc813de5eb6fcaed0dca1e996cdc228ccac1c0065d0c83eb7d1b697d; passport_fe_beating_status=true; tt_scid=xOJmWMxEZGsbdioWazr17pFNwm8OrYTYLJ2dO01TVpy4KeOvmYDHGWCfSKg1TdFl4dbf'}
2025-08-05 06:03:31 | DEBUG | [DoubaoVideoSearch] 请求体: {"messages": [{"content": "{\"text\": \"\\u5e2e\\u6211\\u627e\\u4e00\\u4e9b\\u5173\\u4e8e\\u53cc\\u500d\\u5feb\\u4e50\\u7684\\u6296\\u97f3\\u89c6\\u9891\"}", "content_type": 2001, "attachments": [], "references": []}], "completion_option": {"is_regen": false, "with_suggest": true, "need_create_conversation": true, "launch_stage": 1, "is_replace": false, "is_delete": false, "message_from": 0, "use_deep_think": false, "use_auto_cot": true, "resend_for_regen": false, "event_id": "0"}, "evaluate_option": {"web_ab_params": ""}, "conversation_id": "0", "local_conversation_id": "local_1754345011089", "local_message_id": "36646b93-8946-470b-bfb1-5110fca9816d"}
2025-08-05 06:03:31 | DEBUG | [DoubaoVideoSearch] 请求URL: https://www.doubao.com/samantha/chat/completion?aid=497858&device_id=7532989318484657699&device_plat...
2025-08-05 06:03:31 | DEBUG | [DoubaoVideoSearch] 请求数据大小: 656 字符
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 收到响应，状态码: 200
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 响应头: {'server': 'Tengine', 'content-type': 'text/event-stream', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'date': 'Mon, 04 Aug 2025 22:03:41 GMT', 'cache-control': 'no-cache', 'x-tt-agw-login': '1', 'x-tt-logid': '20250805060341A8ECDBEF18247D0063ED', 'server-timing': 'inner; dur=491,tt_agw; dur=484', 'x-ms-token': 'PNJz0FZ_QYc472fM0RsdB0Nx_AOXZtLrF0SvnRC-FjE6XqFwUAnJlqLomguUVMAWvB35ki9k7KIhOnHEPDzNxYFUACeFaRGQ6zt7DxIq', 'x-envoy-response-flags': '-', 'x-tt-trace-host': '01a904f32dfda387cbf0c81d4c4d78f48a57c7d8f6d38a8c14808bec1094876f84a2f0166a1ec2bd4b6a97e52830b184ee10cb3fd7738ad44a2efeba51ea1863d9f69b892afad70f5910854122581736eb5cf3cda5446fcbe9153a148b0fc489999080c7868b130584bfa64bac53a0ca20', 'x-tt-trace-tag': 'id=03;cdn-cache=miss;type=dyn', 'x-tt-trace-id': '00-250805060341A8ECDBEF18247D0063ED-057FAA2572704A4D-00', 'x-tt-timestamp': '1754345021.556', 'via': 'ens-cache23.cn7026[533,0]', 'timing-allow-origin': '*', 'eagleid': 'db999f2b17543450210426442e'}
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 响应内容长度: 36145 字节
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 开始处理响应流数据
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 开始处理SSE响应流
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 响应数据类型: <class 'bytes'>, 大小: 36145 字节
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 解码后数据长度: 35167 字符
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 处理第 1 个事件，数据长度: 323
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 事件类型: 2002
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 处理第 2 个事件，数据长度: 548
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 事件类型: 2010
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 检测到视频搜索意图
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 处理第 3 个事件，数据长度: 571
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 处理第 4 个事件，数据长度: 683
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 处理第 5 个事件，数据长度: 30976
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 收到视频搜索内容
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 找到 20 个视频卡片
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 1: 姐妹成双 快乐加倍-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 2: 这是你想要的双倍快乐嘛-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 3: 快艾特你兄弟一起来看-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 4: 这就是你们想要的双倍快乐嘛-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 5: 是你们想要的双倍快乐吗-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 6: 你的双C女仆-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 7: 双倍快乐-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 8: 双倍的快乐-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 9: #双倍快乐#慢摇-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 10: 看到最后双倍快乐哟-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 11: 双倍快乐，摇起来呀！-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 12: 双倍快乐你爱了吗-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 13: #双倍快乐#短裙#好身材秀出来兄弟们双倍快乐来咯 #美少女 ...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 14: #无不良和低俗暗示正常穿搭 #美女 #个性搭配 #完美身材 ...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 15: 双胞胎  双倍快乐  快乐翻倍-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 16: #这谁顶得住啊 #小蛮腰扭起来 #完美身材-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 17: 客观评价擦边真的不是很严重，可用 这种文案的真的是不是太猎奇...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 18: #抖音创作者大会 #黑丝#正常穿搭无不良引导 #大长腿-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 19: 秋天第一份双倍快乐-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 视频 20: 双倍快乐-抖音...
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 累计收集到 20 个视频
2025-08-05 06:03:36 | INFO | [DoubaoVideoSearch] 完成状态：立即返回 20 个视频结果
2025-08-05 06:03:36 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 获取到 20 个视频结果
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 开始处理搜索结果
2025-08-05 06:03:36 | DEBUG | [DoubaoVideoSearch] 开始处理视频消息，结果类型: videos
2025-08-05 06:03:36 | INFO | [DoubaoVideoSearch] 收到20个视频结果
2025-08-05 06:03:36 | INFO | 发送app消息: 对方wxid:55878994168@chatroom 类型:19 xml:<appmsg appid="" sdkver="0"><title>🎬 双倍快乐 - 视频搜索结果</title><des>找到 20 个视频，回复数字选择播放</des><action>view</action><type>19</type><showtype>0</showtype><content/><url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url><lowurl/><dataurl/><lowdataurl/><recorditem><![CDATA[<recordinfo><title>🎬 视频搜索结果</title><desc>找到 20 个关于「双倍快乐」的视频，显示前 10 个</desc><datalist count="10"><dataitem datatype="1" dataid="video_1_1754345016" datasourceid="source_1_1754345016"><datadesc>姐妹成双 快乐加倍-抖音</datadesc><sourcename>视频 1</sourcename><sourceheadurl>http://wx.qlogo.cn/mmhead/ver_1/0/0/132</sourceheadurl><sourcetime>2025-01-01&#x20;12:01:00</sourcetime><srcMsgCreateTime>1754345016451</srcMsgCreateTime><fromnewmsgid>source_1_1754345016</fromnewmsgid><dataitemsource><hashusername>hash_1</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="video_2_1754345016" datasourceid="source_2_1754345016"><datadesc>这是你想要的双倍快乐嘛-抖音</datadesc><sourcename>视频 2</sourcename><sourceheadurl>http://wx.qlogo.cn/mmhead/ver_1/0/0/132</sourceheadurl><sourcetime>2025-01-01&#x20;12:02:00</sourcetime><srcMsgCreateTime>1754345016451</srcMsgCreateTime><fromnewmsgid>source_2_1754345016</fromnewmsgid><dataitemsource><hashusername>hash_2</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="video_3_1754345016" datasourceid="source_3_1754345016"><datadesc>快艾特你兄弟一起来看-抖音</datadesc><sourcename>视频 3</sourcename><sourceheadurl>http://wx.qlogo.cn/mmhead/ver_1/0/0/132</sourceheadurl><sourcetime>2025-01-01&#x20;12:03:00</sourcetime><srcMsgCreateTime>1754345016451</srcMsgCreateTime><fromnewmsgid>source_3_1754345016</fromnewmsgid><dataitemsource><hashusername>hash_3</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="video_4_1754345016" datasourceid="source_4_1754345016"><datadesc>这就是你们想要的双倍快乐嘛-抖音</datadesc><sourcename>视频 4</sourcename><sourceheadurl>http://wx.qlogo.cn/mmhead/ver_1/0/0/132</sourceheadurl><sourcetime>2025-01-01&#x20;12:04:00</sourcetime><srcMsgCreateTime>1754345016451</srcMsgCreateTime><fromnewmsgid>source_4_1754345016</fromnewmsgid><dataitemsource><hashusername>hash_4</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="video_5_1754345016" datasourceid="source_5_1754345016"><datadesc>是你们想要的双倍快乐吗-抖音</datadesc><sourcename>视频 5</sourcename><sourceheadurl>http://wx.qlogo.cn/mmhead/ver_1/0/0/132</sourceheadurl><sourcetime>2025-01-01&#x20;12:05:00</sourcetime><srcMsgCreateTime>1754345016451</srcMsgCreateTime><fromnewmsgid>source_5_1754345016</fromnewmsgid><dataitemsource><hashusername>hash_5</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="video_6_1754345016" datasourceid="source_6_1754345016"><datadesc>你的双C女仆-抖音</datadesc><sourcename>视频 6</sourcename><sourceheadurl>http://wx.qlogo.cn/mmhead/ver_1/0/0/132</sourceheadurl><sourcetime>2025-01-01&#x20;12:06:00</sourcetime><srcMsgCreateTime>1754345016451</srcMsgCreateTime><fromnewmsgid>source_6_1754345016</fromnewmsgid><dataitemsource><hashusername>hash_6</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="video_7_1754345016" datasourceid="source_7_1754345016"><datadesc>双倍快乐-抖音</datadesc><sourcename>视频 7</sourcename><sourceheadurl>http://wx.qlogo.cn/mmhead/ver_1/0/0/132</sourceheadurl><sourcetime>2025-01-01&#x20;12:07:00</sourcetime><srcMsgCreateTime>1754345016451</srcMsgCreateTime><fromnewmsgid>source_7_1754345016</fromnewmsgid><dataitemsource><hashusername>hash_7</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="video_8_1754345016" datasourceid="source_8_1754345016"><datadesc>双倍的快乐-抖音</datadesc><sourcename>视频 8</sourcename><sourceheadurl>http://wx.qlogo.cn/mmhead/ver_1/0/0/132</sourceheadurl><sourcetime>2025-01-01&#x20;12:08:00</sourcetime><srcMsgCreateTime>1754345016451</srcMsgCreateTime><fromnewmsgid>source_8_1754345016</fromnewmsgid><dataitemsource><hashusername>hash_8</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="video_9_1754345016" datasourceid="source_9_1754345016"><datadesc>#双倍快乐#慢摇-抖音</datadesc><sourcename>视频 9</sourcename><sourceheadurl>http://wx.qlogo.cn/mmhead/ver_1/0/0/132</sourceheadurl><sourcetime>2025-01-01&#x20;12:09:00</sourcetime><srcMsgCreateTime>1754345016451</srcMsgCreateTime><fromnewmsgid>source_9_1754345016</fromnewmsgid><dataitemsource><hashusername>hash_9</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="video_10_1754345016" datasourceid="source_10_1754345016"><datadesc>看到最后双倍快乐哟-抖音</datadesc><sourcename>视频 10</sourcename><sourceheadurl>http://wx.qlogo.cn/mmhead/ver_1/0/0/132</sourceheadurl><sourcetime>2025-01-01&#x20;12:10:00</sourcetime><srcMsgCreateTime>1754345016451</srcMsgCreateTime><fromnewmsgid>source_10_1754345016</fromnewmsgid><dataitemsource><hashusername>hash_10</hashusername></dataitemsource></dataitem></datalist><favcreatetime>1754345016451</favcreatetime></recordinfo>]]></recorditem><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><extinfo/><sourceusername/><sourcedisplayname/><thumburl/><md5/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-08-05 06:03:36 | INFO | [DoubaoVideoSearch] 视频列表聊天记录发送成功
2025-08-05 06:03:38 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:💡 回复数字选择视频（如：1）
2025-08-05 06:03:38 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 7.69秒
2025-08-05 06:03:38 | INFO | 成功加载表情映射文件，共 552 条记录
2025-08-05 06:03:38 | DEBUG | 处理消息内容: '找视频 双倍快乐'
2025-08-05 06:03:38 | DEBUG | 消息内容 '找视频 双倍快乐' 不匹配任何命令，忽略
2025-08-05 06:03:53 | DEBUG | 收到消息: {'MsgId': 970881251, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n3'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754345043, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_HTWouXKD|v1_XJyF2Wup</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 3', 'NewMsgId': 6140448300786368468, 'MsgSeq': 871427279}
2025-08-05 06:03:53 | INFO | 收到文本消息: 消息ID:970881251 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:3
2025-08-05 06:03:53 | DEBUG | [DouBaoImageToImage] 收到文本消息: '3' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 06:03:53 | DEBUG | [DouBaoImageToImage] 命令解析: ['3']
2025-08-05 06:03:53 | INFO | [DoubaoVideoSearch] 用户选择播放视频 3: 快艾特你兄弟一起来看-抖音...
2025-08-05 06:03:54 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-05 06:03:54 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7461062739780259130/?scene_from=douyin_h5_flow
2025-08-05 06:03:54 | DEBUG | [DoubaoVideoSearch] 发送视频解析请求: http://api.xn--ei1aa.cn/API/douyin.php?url=https%3A//m.douyin.com/share/video/7461062739780259130/%3Fscene_from%3Ddouyin_h5_flow
2025-08-05 06:03:55 | DEBUG | [DoubaoVideoSearch] 视频解析API响应状态码: 200
2025-08-05 06:03:55 | DEBUG | [DoubaoVideoSearch] 视频解析API响应: {'code': 200, 'msg': '解析成功', 'data': {'author': '小欣女神～', 'uid': 93590949548, 'avatar': 'https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-i-0813_ogdL1hAFeAIoTfEjAGsewfRejIEoLHA3ixAQJc.jpeg?from=327834062', 'like': 168, 'time': **********, 'title': '快艾特你兄弟一起来看 #双倍快乐 #美女合集 #清纯美女 #你要怎么抵抗我 #养眼', 'cover': 'https://p3-sign.douyinpic.com/tos-cn-p-0015c000-ce/oE7ovJzSA09gQAJPEg8LIoVFQCieeVfs7gGFBH~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=89nuUTjPIm1HdySfwT8i0JpXCnE%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=20250805060405B27A5CA00DB1AA965282', 'url': 'https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000cu5ga6fog65t0u4iak0g&ratio=720p&line=0', 'music': {'author': '小欣女神～', 'avatar': 'https://p3.douyinpic.com/aweme/1080x1080/aweme-avatar/tos-cn-i-0813_ogdL1hAFeAIoTfEjAGsewfRejIEoLHA3ixAQJc.jpeg?from=327834062'}}}
2025-08-05 06:03:55 | DEBUG | [DoubaoVideoSearch] 提取的视频信息: {'media_url': 'https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000cu5ga6fog65t0u4iak0g&ratio=720p&line=0', 'artist_name': '小欣女神～', 'song_name': '快艾特你兄弟一起来看 #双倍快乐 #美女合集 #清纯美女 #你要怎么抵抗我 #养眼', 'img_url': 'https://p3-sign.douyinpic.com/tos-cn-p-0015c000-ce/oE7ovJzSA09gQAJPEg8LIoVFQCieeVfs7gGFBH~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=89nuUTjPIm1HdySfwT8i0JpXCnE%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=20250805060405B27A5CA00DB1AA965282'}
2025-08-05 06:03:55 | INFO | [DoubaoVideoSearch] 视频解析成功: 快艾特你兄弟一起来看 #双倍快乐 #美女合集 #清纯美女 #你要怎么抵抗我 #养眼 by 小欣女神～
2025-08-05 06:03:55 | INFO | [DoubaoVideoSearch] 视频解析成功: 快艾特你兄弟一起来看 #双倍快乐 #美女合集 #清纯美女 #你要怎么抵抗我 #养眼
2025-08-05 06:03:55 | DEBUG | [DoubaoVideoSearch] 开始发送视频分享消息到 55878994168@chatroom
2025-08-05 06:03:55 | DEBUG | [DoubaoVideoSearch] 视频信息 - 标题: 快艾特你兄弟一起来看 #双倍快乐 #美女合集 #清纯美女 #你要怎么抵抗我 #养眼, 描述: 作者: 小欣女神～
2025-08-05 06:03:55 | DEBUG | [DoubaoVideoSearch] 视频URL: https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000cu5ga6fog65t0u4iak0g&ratio=720p&line=0...
2025-08-05 06:03:55 | DEBUG | [DoubaoVideoSearch] XML消息长度: 1312 字符
2025-08-05 06:03:55 | INFO | 发送app消息: 对方wxid:55878994168@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>快艾特你兄弟一起来看 #双倍快乐 #美女合集 #清纯美女 #你要怎么抵抗我 #养眼</title><des>作者: 小欣女神～</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000cu5ga6fog65t0u4iak0g&ratio=720p&line=0</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p3-sign.douyinpic.com/tos-cn-p-0015c000-ce/oE7ovJzSA09gQAJPEg8LIoVFQCieeVfs7gGFBH~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=89nuUTjPIm1HdySfwT8i0JpXCnE%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=20250805060405B27A5CA00DB1AA965282</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-08-05 06:03:55 | INFO | [DoubaoVideoSearch] 视频分享消息发送成功，client_msg_id: 55878994168@chatroom_1754345035, new_msg_id: 9163318004557495476
2025-08-05 06:03:55 | DEBUG | 处理消息内容: '3'
2025-08-05 06:03:55 | DEBUG | 消息内容 '3' 不匹配任何命令，忽略
2025-08-05 06:04:05 | DEBUG | 收到消息: {'MsgId': 2052160910, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n1'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754345054, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_at5mGpZZ|v1_3X+iJGpq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 1', 'NewMsgId': 5540836224006032108, 'MsgSeq': 871427284}
2025-08-05 06:04:05 | INFO | 收到文本消息: 消息ID:2052160910 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:1
2025-08-05 06:04:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '1' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 06:04:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['1']
2025-08-05 06:04:05 | INFO | [DoubaoVideoSearch] 用户选择播放视频 1: 姐妹成双 快乐加倍-抖音...
2025-08-05 06:04:06 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-05 06:04:06 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7508351341421530426/?scene_from=douyin_h5_flow
2025-08-05 06:04:06 | DEBUG | [DoubaoVideoSearch] 发送视频解析请求: http://api.xn--ei1aa.cn/API/douyin.php?url=https%3A//m.douyin.com/share/video/7508351341421530426/%3Fscene_from%3Ddouyin_h5_flow
2025-08-05 06:04:07 | DEBUG | [DoubaoVideoSearch] 视频解析API响应状态码: 200
2025-08-05 06:04:07 | DEBUG | [DoubaoVideoSearch] 视频解析API响应: {'code': 200, 'msg': '解析成功', 'data': {'author': '卢小熙', 'uid': 93599133506, 'avatar': 'https://p11.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_3336851e14694ba7a61f0bcdd393b011.jpeg?from=327834062', 'like': 19048, 'time': **********, 'title': '姐妹成双 快乐加倍 #你要怎么抵抗我 #双倍快乐', 'cover': 'https://p3-sign.douyinpic.com/tos-cn-i-0813c000-ce/o8AibBbcE80AehGE8zUBfEmeLiAiAAYwAPy0Ii~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=xeruwS95HyP1XHJe4LFQG8BBS%2BM%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=2025080506041691E07BD982D44779D531', 'url': 'https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000d0pgavnog65p3rk542n0&ratio=720p&line=0', 'music': {'author': '林婉', 'avatar': 'https://p11.douyinpic.com/aweme/1080x1080/aweme-avatar/tos-cn-avt-0015_8b4424f6dfc8f08a9d0b43cda216013e.jpeg?from=327834062'}}}
2025-08-05 06:04:07 | DEBUG | [DoubaoVideoSearch] 提取的视频信息: {'media_url': 'https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000d0pgavnog65p3rk542n0&ratio=720p&line=0', 'artist_name': '卢小熙', 'song_name': '姐妹成双 快乐加倍 #你要怎么抵抗我 #双倍快乐', 'img_url': 'https://p3-sign.douyinpic.com/tos-cn-i-0813c000-ce/o8AibBbcE80AehGE8zUBfEmeLiAiAAYwAPy0Ii~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=xeruwS95HyP1XHJe4LFQG8BBS%2BM%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=2025080506041691E07BD982D44779D531'}
2025-08-05 06:04:07 | INFO | [DoubaoVideoSearch] 视频解析成功: 姐妹成双 快乐加倍 #你要怎么抵抗我 #双倍快乐 by 卢小熙
2025-08-05 06:04:07 | INFO | [DoubaoVideoSearch] 视频解析成功: 姐妹成双 快乐加倍 #你要怎么抵抗我 #双倍快乐
2025-08-05 06:04:07 | DEBUG | [DoubaoVideoSearch] 开始发送视频分享消息到 55878994168@chatroom
2025-08-05 06:04:07 | DEBUG | [DoubaoVideoSearch] 视频信息 - 标题: 姐妹成双 快乐加倍 #你要怎么抵抗我 #双倍快乐, 描述: 作者: 卢小熙
2025-08-05 06:04:07 | DEBUG | [DoubaoVideoSearch] 视频URL: https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000d0pgavnog65p3rk542n0&ratio=720p&line=0...
2025-08-05 06:04:07 | DEBUG | [DoubaoVideoSearch] XML消息长度: 1295 字符
2025-08-05 06:04:07 | INFO | 发送app消息: 对方wxid:55878994168@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>姐妹成双 快乐加倍 #你要怎么抵抗我 #双倍快乐</title><des>作者: 卢小熙</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000d0pgavnog65p3rk542n0&ratio=720p&line=0</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p3-sign.douyinpic.com/tos-cn-i-0813c000-ce/o8AibBbcE80AehGE8zUBfEmeLiAiAAYwAPy0Ii~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=xeruwS95HyP1XHJe4LFQG8BBS%2BM%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=2025080506041691E07BD982D44779D531</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-08-05 06:04:07 | INFO | [DoubaoVideoSearch] 视频分享消息发送成功，client_msg_id: 55878994168@chatroom_1754345047, new_msg_id: 8194522306031757990
2025-08-05 06:04:07 | DEBUG | 处理消息内容: '1'
2025-08-05 06:04:07 | DEBUG | 消息内容 '1' 不匹配任何命令，忽略
