2025-08-05 05:56:54 | SUCCESS | 读取主设置成功
2025-08-05 05:56:54 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-05 05:56:55 | INFO | 2025/08/05 05:56:55 GetRedisAddr: 127.0.0.1:6379
2025-08-05 05:56:55 | INFO | 2025/08/05 05:56:55 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-05 05:56:55 | INFO | 2025/08/05 05:56:55 Server start at :9000
2025-08-05 05:56:55 | SUCCESS | WechatAPI服务已启动
2025-08-05 05:56:55 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-05 05:56:55 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-05 05:56:55 | SUCCESS | 登录成功
2025-08-05 05:56:55 | SUCCESS | 已开启自动心跳
2025-08-05 05:56:55 | INFO | 成功加载表情映射文件，共 552 条记录
2025-08-05 05:56:55 | SUCCESS | 数据库初始化成功
2025-08-05 05:56:55 | SUCCESS | 定时任务已启动
2025-08-05 05:56:56 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-05 05:56:56 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 05:56:57 | INFO | 播客API初始化成功
2025-08-05 05:56:57 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 05:56:57 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-05 05:56:57 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-05 05:56:57 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-05 05:56:57 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-05 05:56:57 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-05 05:56:57 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-05 05:56:57 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-05 05:56:57 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-05 05:56:58 | INFO | [ChatSummary] 数据库初始化成功
2025-08-05 05:56:58 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-05 05:56:58 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-05 05:56:58 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-05 05:56:58 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-05 05:56:58 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-05 05:56:58 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-05 05:56:58 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-05 05:56:58 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-05 05:56:58 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-05 05:56:58 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-05 05:56:58 | DEBUG |   - 启用状态: True
2025-08-05 05:56:58 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-05 05:56:58 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-05 05:56:58 | DEBUG |   - Web ID: 7532989324985157172
2025-08-05 05:56:58 | DEBUG |   - Cookies配置: 已配置
2025-08-05 05:56:58 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-08-05 05:56:58 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-05 05:56:58 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-05 05:56:58 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-05 05:56:58 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-05 05:56:58 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-05 05:56:58 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-05 05:56:58 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-05 05:56:58 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 05:56:58 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-05 05:56:58 | INFO | [RenameReminder] 开始启用插件...
2025-08-05 05:56:58 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-05 05:56:58 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-05 05:56:58 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-05 05:56:58 | INFO | 已设置检查间隔为 3600 秒
2025-08-05 05:56:58 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-05 05:56:59 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-05 05:56:59 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-05 05:57:00 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-05 05:57:00 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-05 05:57:01 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-05 05:57:01 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-05 05:57:01 | INFO | [yuanbao] 插件初始化完成
2025-08-05 05:57:01 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-05 05:57:01 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-05 05:57:01 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-05 05:57:01 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-05 05:57:01 | INFO | 处理堆积消息中
2025-08-05 05:57:01 | SUCCESS | 处理堆积消息完毕
2025-08-05 05:57:01 | SUCCESS | 开始处理消息
2025-08-05 05:57:04 | DEBUG | 收到消息: {'MsgId': 1951967235, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 双倍快乐'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754344634, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_1kjJ7FcR|v1_tk7VYobw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 双倍快乐', 'NewMsgId': 3501790696381247929, 'MsgSeq': 871427256}
2025-08-05 05:57:04 | INFO | 收到文本消息: 消息ID:1951967235 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 双倍快乐
2025-08-05 05:57:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '找视频 双倍快乐' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 05:57:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['找视频', '双倍快乐']
2025-08-05 05:57:05 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-05 05:57:05 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 双倍快乐 from wxid_ubbh6q832tcs21
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 开始处理视频搜索请求，用户: wxid_ubbh6q832tcs21
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 用户限制检查 - 用户: wxid_ubbh6q832tcs21, 上次请求: 1754344625.54秒前, 等待时间: 0.00秒
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 令牌桶状态: 5.00/5
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 成功获取令牌，剩余: 4.00
2025-08-05 05:57:05 | INFO | [DoubaoVideoSearch] 开始搜索视频，关键词: '双倍快乐'
2025-08-05 05:57:05 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 双倍快乐
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 调试信息 - 设备ID: 7532989318484657699, Web ID: 7532989324985157172
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 调试信息 - Cookies长度: 2200
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 第1次尝试，开始构造请求参数
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 生成会话ID: local_1754344625542, 消息ID: 55755db2-430a-4812-b9e9-be0f691cc915
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 构造搜索查询: 帮我找一些关于双倍快乐的抖音视频
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 请求头: {'Host': 'www.doubao.com', 'Connection': 'keep-alive', 'x-flow-trace': '04-5d4a0f433c2e4f30-d2b4398f40bc4c7e-01', 'sec-ch-ua-platform': '"Android"', 'sec-ch-ua': '"Chromium";v="130", "Android WebView";v="130", "Not?A_Brand";v="99"', 'sec-ch-ua-mobile': '?1', 'Agw-Js-Conv': 'str, str', 'last-event-id': 'undefined', 'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PJD110 Build/AP3A.240617.008) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36', 'content-type': 'application/json', 'Accept': '*/*', 'Origin': 'https://www.doubao.com', 'X-Requested-With': 'mark.via', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'https://www.doubao.com/chat/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Cookie': 'i18next=zh; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MTQ5MzU5NjkzNjgwNzQxfQ==.yuE0xZkkRp6rqlY66YCqWrFlfN//9LZZZ7S0sT8jJho=; flow_user_country=CN; ttcid=9998073e514b46379ccfd15657ffa06c33; s_v_web_id=verify_mdqh7kzd_ZkQmYYgh_AW2y_4mCs_95OU_aDZp9boGLZ1u; passport_csrf_token=23cc9202dee0bd3af72b1d67305320a1; passport_csrf_token_default=23cc9202dee0bd3af72b1d67305320a1; hook_slardar_session_id=2025073105274644B91E1243B31013D640,ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910866%7Cd0f892d1e4c839a868e20cdb539884dac45992241cb359c30ef040897bbe4427; passport_mfa_token=CjGPhXlg8y7V4IKl5gnYMvo0%2BqNhIn32FEYWNz6gkjjYI4X8hnKQYV7Unk%2BU0WC%2FzeuwGkoKPAAAAAAAAAAAAABPS%2FOfnAf%2F4h5UzB7KpogdYZuez0H1rnw99uy9NGTKi1MfFnCWCMxSVLE%2B%2FXAZYG41LRChlfgNGPax0WwgAiIBAyxRQuI%3D; d_ticket=3ba103731f1417bb3d92f65489c2cf384b9ef; odin_tt=ceee7eb54d94684dae2543622104f0c4c54b16bf7bf53e2998c4381989c54b7a65d33009560a1c9211cc7ee74550542d19171565610c7c33ca884b16e4c1d1b0; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=1e0467e7ce1de60752facaf3e7239ed9%2C; passport_auth_status_ss=1e0467e7ce1de60752facaf3e7239ed9%2C; sid_guard=53c1e5576dbeb67c1f781749fa771e22%7C1753910897%7C5184000%7CSun%2C+28-Sep-2025+21%3A28%3A17+GMT; uid_tt=b615a4154b708bbf57f415bfbf358f8e; uid_tt_ss=b615a4154b708bbf57f415bfbf358f8e; sid_tt=53c1e5576dbeb67c1f781749fa771e22; sessionid=53c1e5576dbeb67c1f781749fa771e22; sessionid_ss=53c1e5576dbeb67c1f781749fa771e22; session_tlb_tag=sttt%7C6%7CU8HlV22-tnwfeBdJ-nceIv_________SzM3yFwLkszo23AKHohjjcpT0MVXz8bPDuNRWJVotL34%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; ssid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; flow_ssr_sidebar_expand=1; ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910898%7C7dcd3259bc813de5eb6fcaed0dca1e996cdc228ccac1c0065d0c83eb7d1b697d; passport_fe_beating_status=true; tt_scid=xOJmWMxEZGsbdioWazr17pFNwm8OrYTYLJ2dO01TVpy4KeOvmYDHGWCfSKg1TdFl4dbf'}
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 请求体: {"messages": [{"content": "{\"text\": \"\\u5e2e\\u6211\\u627e\\u4e00\\u4e9b\\u5173\\u4e8e\\u53cc\\u500d\\u5feb\\u4e50\\u7684\\u6296\\u97f3\\u89c6\\u9891\"}", "content_type": 2001, "attachments": [], "references": []}], "completion_option": {"is_regen": false, "with_suggest": true, "need_create_conversation": true, "launch_stage": 1, "is_replace": false, "is_delete": false, "message_from": 0, "use_deep_think": false, "use_auto_cot": true, "resend_for_regen": false, "event_id": "0"}, "evaluate_option": {"web_ab_params": ""}, "conversation_id": "0", "local_conversation_id": "local_1754344625542", "local_message_id": "55755db2-430a-4812-b9e9-be0f691cc915"}
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 请求URL: https://www.doubao.com/samantha/chat/completion?aid=497858&device_id=7532989318484657699&device_plat...
2025-08-05 05:57:05 | DEBUG | [DoubaoVideoSearch] 请求数据大小: 656 字符
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 收到响应，状态码: 200
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 响应头: {'server': 'Tengine', 'content-type': 'text/event-stream', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'date': 'Mon, 04 Aug 2025 21:57:16 GMT', 'cache-control': 'no-cache', 'x-tt-agw-login': '1', 'x-tt-logid': '20250805055715CC25A327AB351E5EF7B5', 'server-timing': 'inner; dur=459,tt_agw; dur=450', 'x-ms-token': '6vwX87v3nQH_mTp4qDOkr3ZB2ycIvD53jLZM2ynmFwQDCpdF4TB1qqgZo2cwN7vIup2nzu9-mTpomyMNwzN8pFlaPi6_Hn0_2LW_r-OR', 'x-envoy-response-flags': '-', 'x-tt-trace-host': '01a904f32dfda387cbf0c81d4c4d78f48a57c7d8f6d38a8c14808bec1094876f84e2c59ff35dc30e7230df00d7b5851740473aa69529cec1cff91b8daefa5c984465a1fb35496478263f97c8d480249f3a2efed03906147ad99ff9ea05545f73b0fab28157c7eef97be2b3d296349b1542', 'x-tt-trace-tag': 'id=03;cdn-cache=miss;type=dyn', 'x-tt-trace-id': '00-250805055715CC25A327AB351E5EF7B5-2A89E84416FFAF11-00', 'x-tt-timestamp': '1754344636.039', 'via': 'ens-cache29.cn7026[573,0]', 'timing-allow-origin': '*', 'eagleid': 'db999f3117543446354906965e'}
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 响应内容长度: 36061 字节
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 开始处理响应流数据
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 开始处理SSE响应流
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 响应数据类型: <class 'bytes'>, 大小: 36061 字节
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 解码后数据长度: 35153 字符
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 处理第 1 个事件，数据长度: 323
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 事件类型: 2002
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 处理第 2 个事件，数据长度: 548
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 事件类型: 2010
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 检测到视频搜索意图
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 处理第 3 个事件，数据长度: 571
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 处理第 4 个事件，数据长度: 683
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 处理第 5 个事件，数据长度: 30974
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 事件类型: 2001
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 收到视频搜索内容
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 找到 20 个视频卡片
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 1: 姐妹成双 快乐加倍-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 2: 这是你想要的双倍快乐嘛-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 3: 快艾特你兄弟一起来看-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 4: 是你们想要的双倍快乐吗-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 5: 双倍快乐-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 6: 你的双C女仆-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 7: 双倍的快乐-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 8: 双倍快乐，摇起来呀！-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 9: #双倍快乐#慢摇-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 10: 双倍快乐你爱了吗-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 11: 看到最后双倍快乐哟-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 12: #双倍快乐#短裙#好身材秀出来兄弟们双倍快乐来咯 #美少女 ...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 13: 双倍快乐～-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 14: 双胞胎  双倍快乐  快乐翻倍-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 15: #这谁顶得住啊 #小蛮腰扭起来 #完美身材-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 16: 客观评价擦边真的不是很严重，可用 这种文案的真的是不是太猎奇...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 17: #抖音创作者大会 #黑丝#正常穿搭无不良引导 #大长腿-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 18: 双倍快乐-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 19: 请用一个词形容她-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 视频 20: 双倍快乐-抖音...
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 累计收集到 20 个视频
2025-08-05 05:57:11 | INFO | [DoubaoVideoSearch] 完成状态：立即返回 20 个视频结果
2025-08-05 05:57:11 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 获取到 20 个视频结果
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 开始处理搜索结果
2025-08-05 05:57:11 | DEBUG | [DoubaoVideoSearch] 开始处理视频消息，结果类型: videos
2025-08-05 05:57:11 | INFO | [DoubaoVideoSearch] 收到20个视频结果
2025-08-05 05:57:12 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 🎬 找到 20 个关于「双倍快乐」的视频：

1. 姐妹成双 快乐加倍-抖音
2. 这是你想要的双倍快乐嘛-抖音
3. 快艾特你兄弟一起来看-抖音
4. 是你们想要的双倍快乐吗-抖音
5. 双倍快乐-抖音
6. 你的双C女仆-抖音
7. 双倍的快乐-抖音
8. 双倍快乐，摇起来呀！-抖音
9. #双倍快乐#慢摇-抖音
10. 双倍快乐你爱了吗-抖音

... 还有 10 个视频未显示

💡 回复数字选择视频（如：1）
2025-08-05 05:57:12 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 7.07秒
2025-08-05 05:57:12 | INFO | 成功加载表情映射文件，共 552 条记录
2025-08-05 05:57:12 | DEBUG | 处理消息内容: '找视频 双倍快乐'
2025-08-05 05:57:12 | DEBUG | 消息内容 '找视频 双倍快乐' 不匹配任何命令，忽略
2025-08-05 05:57:22 | DEBUG | 收到消息: {'MsgId': 513673368, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n4'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754344651, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_ddq283xh|v1_RJPHHwij</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 4', 'NewMsgId': 597857997977789535, 'MsgSeq': 871427261}
2025-08-05 05:57:22 | INFO | 收到文本消息: 消息ID:513673368 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:4
2025-08-05 05:57:22 | DEBUG | [DouBaoImageToImage] 收到文本消息: '4' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 05:57:22 | DEBUG | [DouBaoImageToImage] 命令解析: ['4']
2025-08-05 05:57:22 | DEBUG | [DoubaoVideoSearch] 用户限制检查 - 用户: wxid_ubbh6q832tcs21, 上次请求: 17.35秒前, 等待时间: 42.65秒
2025-08-05 05:57:22 | DEBUG | [DoubaoVideoSearch] 用户 wxid_ubbh6q832tcs21 请求过于频繁，需等待 42.6 秒
2025-08-05 05:57:23 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 慢点慢点，等 42.6 秒再来
2025-08-05 05:57:23 | DEBUG | 处理消息内容: '4'
2025-08-05 05:57:23 | DEBUG | 消息内容 '4' 不匹配任何命令，忽略
2025-08-05 05:58:05 | DEBUG | 收到消息: {'MsgId': 727829958, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n5'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754344694, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_GlDQTZTt|v1_gHy5PHDr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 5', 'NewMsgId': 5832091709273237948, 'MsgSeq': 871427264}
2025-08-05 05:58:05 | INFO | 收到文本消息: 消息ID:727829958 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:5
2025-08-05 05:58:05 | DEBUG | [DouBaoImageToImage] 收到文本消息: '5' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 05:58:05 | DEBUG | [DouBaoImageToImage] 命令解析: ['5']
2025-08-05 05:58:05 | DEBUG | [DoubaoVideoSearch] 用户限制检查 - 用户: wxid_ubbh6q832tcs21, 上次请求: 60.19秒前, 等待时间: 0.00秒
2025-08-05 05:58:05 | DEBUG | [DoubaoVideoSearch] 令牌桶状态: 5.00/5
2025-08-05 05:58:05 | DEBUG | [DoubaoVideoSearch] 成功获取令牌，剩余: 4.00
2025-08-05 05:58:05 | INFO | [DoubaoVideoSearch] 用户选择播放视频 5: 双倍快乐-抖音...
2025-08-05 05:58:06 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:ae672c700aaf271a151e18a9ecf4445b 总长度:30259
2025-08-05 05:58:06 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7474849658074877247/?scene_from=douyin_h5_flow
2025-08-05 05:58:06 | DEBUG | [DoubaoVideoSearch] 发送视频解析请求: http://api.xn--ei1aa.cn/API/douyin.php?url=https%3A//m.douyin.com/share/video/7474849658074877247/%3Fscene_from%3Ddouyin_h5_flow
2025-08-05 05:58:07 | DEBUG | [DoubaoVideoSearch] 视频解析API响应状态码: 200
2025-08-05 05:58:07 | DEBUG | [DoubaoVideoSearch] 视频解析API响应: {'code': 200, 'msg': '解析成功', 'data': {'author': '哇塞哥', 'uid': 52864401555, 'avatar': 'https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-i-0813_oEAEAn3CIphEAUF9AdQArMANyegfcTjOAFpIFD.jpeg?from=327834062', 'like': 677, 'time': **********, 'title': '双倍快乐 #身材很哇塞 好#这样的身材有人喜欢么 #这魔性的身材', 'cover': 'https://p26-sign.douyinpic.com/tos-cn-p-0015/owNIvGA9uIKuAA4AEnAgRADpF9fBABI6VQp4Te~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=2%2B6AcmRrlJ77peZQ%2Fl7Z5k%2Fs28w%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=20250805055816420F01D6AC610A8A8913', 'url': 'https://aweme.snssdk.com/aweme/v1/play/?video_id=v0d00fg10000cuu00ffog65kin2s8n1g&ratio=720p&line=0', 'music': {'author': '哇塞哥', 'avatar': 'https://p3.douyinpic.com/aweme/1080x1080/aweme-avatar/tos-cn-i-0813_oEAEAn3CIphEAUF9AdQArMANyegfcTjOAFpIFD.jpeg?from=327834062'}}}
2025-08-05 05:58:07 | DEBUG | [DoubaoVideoSearch] 提取的视频信息: {'media_url': 'https://aweme.snssdk.com/aweme/v1/play/?video_id=v0d00fg10000cuu00ffog65kin2s8n1g&ratio=720p&line=0', 'artist_name': '哇塞哥', 'song_name': '双倍快乐 #身材很哇塞 好#这样的身材有人喜欢么 #这魔性的身材', 'img_url': 'https://p26-sign.douyinpic.com/tos-cn-p-0015/owNIvGA9uIKuAA4AEnAgRADpF9fBABI6VQp4Te~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=2%2B6AcmRrlJ77peZQ%2Fl7Z5k%2Fs28w%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=20250805055816420F01D6AC610A8A8913'}
2025-08-05 05:58:07 | INFO | [DoubaoVideoSearch] 视频解析成功: 双倍快乐 #身材很哇塞 好#这样的身材有人喜欢么 #这魔性的身材 by 哇塞哥
2025-08-05 05:58:07 | INFO | [DoubaoVideoSearch] 视频解析成功: 双倍快乐 #身材很哇塞 好#这样的身材有人喜欢么 #这魔性的身材
2025-08-05 05:58:07 | DEBUG | [DoubaoVideoSearch] 开始发送视频分享消息到 55878994168@chatroom
2025-08-05 05:58:07 | DEBUG | [DoubaoVideoSearch] 视频信息 - 标题: 双倍快乐 #身材很哇塞 好#这样的身材有人喜欢么 #这魔性的身材, 描述: 作者: 哇塞哥
2025-08-05 05:58:07 | DEBUG | [DoubaoVideoSearch] 视频URL: https://aweme.snssdk.com/aweme/v1/play/?video_id=v0d00fg10000cuu00ffog65kin2s8n1g&ratio=720p&line=0...
2025-08-05 05:58:07 | DEBUG | [DoubaoVideoSearch] XML消息长度: 1301 字符
2025-08-05 05:58:07 | INFO | 发送app消息: 对方wxid:55878994168@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>双倍快乐 #身材很哇塞 好#这样的身材有人喜欢么 #这魔性的身材</title><des>作者: 哇塞哥</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v0d00fg10000cuu00ffog65kin2s8n1g&ratio=720p&line=0</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p26-sign.douyinpic.com/tos-cn-p-0015/owNIvGA9uIKuAA4AEnAgRADpF9fBABI6VQp4Te~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=2%2B6AcmRrlJ77peZQ%2Fl7Z5k%2Fs28w%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=20250805055816420F01D6AC610A8A8913</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-08-05 05:58:07 | INFO | [DoubaoVideoSearch] 视频分享消息发送成功，client_msg_id: 55878994168@chatroom_1754344687, new_msg_id: 2095073419925837398
2025-08-05 05:58:07 | DEBUG | 处理消息内容: '5'
2025-08-05 05:58:07 | DEBUG | 消息内容 '5' 不匹配任何命令，忽略
2025-08-05 05:58:16 | DEBUG | 收到消息: {'MsgId': 479158430, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n6'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754344705, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_fJNAmYAt|v1_5LIG841L</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 6', 'NewMsgId': 3510027400782604379, 'MsgSeq': 871427269}
2025-08-05 05:58:16 | INFO | 收到文本消息: 消息ID:479158430 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:6
2025-08-05 05:58:16 | DEBUG | [DouBaoImageToImage] 收到文本消息: '6' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-05 05:58:16 | DEBUG | [DouBaoImageToImage] 命令解析: ['6']
2025-08-05 05:58:16 | DEBUG | [DoubaoVideoSearch] 用户限制检查 - 用户: wxid_ubbh6q832tcs21, 上次请求: 11.16秒前, 等待时间: 48.84秒
2025-08-05 05:58:16 | DEBUG | [DoubaoVideoSearch] 用户 wxid_ubbh6q832tcs21 请求过于频繁，需等待 48.8 秒
2025-08-05 05:58:17 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 慢点慢点，等 48.8 秒再来
2025-08-05 05:58:17 | DEBUG | 处理消息内容: '6'
2025-08-05 05:58:17 | DEBUG | 消息内容 '6' 不匹配任何命令，忽略
